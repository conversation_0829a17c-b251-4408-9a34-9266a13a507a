name: Deploy to Vercel (Account B)

on:
  push:
    branches: [master]

jobs:
  deploy:
    runs-on: ubuntu-latest
    env:
      VERCEL_TOKEN: ${{ secrets.VERCEL_TOKEN }}
      VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
      VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}

    steps:
      - uses: actions/checkout@v4

      # 安装 pnpm
      - uses: pnpm/action-setup@v2
        with:
          version: 10.14.0 # 与 Vercel 日志一致

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: "pnpm"

      - name: Install dependencies
        run: pnpm install --frozen-lockfile
        env:
          NEXT_PUBLIC_API_URL: ${{ secrets.NEXT_PUBLIC_API_URL }}

      - name: Build
        run: pnpm build
        env:
          NEXT_PUBLIC_API_URL: ${{ secrets.NEXT_PUBLIC_API_URL }}

      - name: Install Vercel CLI
        run: pnpm add -g vercel

      - name: Pull Vercel environment
        run: vercel pull --yes --environment=production --token $VERCEL_TOKEN

      - name: Build for Vercel
        run: vercel build --prod --token $VERCEL_TOKEN
        env:
          VERCEL_PNPM_VERSION: 10.14.0

      - name: Deploy to Vercel
        run: vercel deploy --prebuilt --prod --token $VERCEL_TOKEN
