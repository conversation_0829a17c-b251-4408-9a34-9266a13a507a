/**
 * 几何计算模块
 * 负责钉子位置计算、线条路径计算等几何相关功能
 */

export class GeometryCalculator {
  constructor(options = {}) {
    this.imageSize = options.imageSize || 500;
    this.numberOfPins = options.numberOfPins || 288; // 36 * 8
    this.minDistance = options.minDistance || 20;
    this.hoopDiameter = options.hoopDiameter || 0.625;
  }

  /**
   * 计算钉子在圆周上的位置
   * @returns {Array<Array<number>>} 钉子坐标数组 [[x, y], ...]
   */
  calculatePinPositions() {
    const pinCoords = [];
    const center = this.imageSize / 2;
    const radius = this.imageSize / 2 - 0.5;

    for (let i = 0; i < this.numberOfPins; i++) {
      const angle = 2 * Math.PI * i / this.numberOfPins;
      const x = Math.floor(center + radius * Math.cos(angle));
      const y = Math.floor(center + radius * Math.sin(angle));
      pinCoords.push([x, y]);
    }

    return pinCoords;
  }

  /**
   * 生成两点之间的线性插值坐标（与原版本完全一致）
   * @param {number} start - 起始值
   * @param {number} end - 结束值
   * @param {number} num - 点的数量
   * @returns {Array<number>} 插值坐标数组
   */
  linspace(start, end, num) {
    if (typeof num === "undefined") {
      num = Math.max(Math.round(end - start) + 1, 1);
    }
    if (num < 2) {
      return num === 1 ? [start] : [];
    }

    const result = new Array(num);
    num--; // 减1，与原版本一致

    for (let i = num; i >= 0; i--) {
      result[i] = Math.floor((i * end + (num - i) * start) / num);
    }

    return result;
  }

  /**
   * 计算两个钉子之间的线条路径
   * @param {Array<number>} pin1 - 第一个钉子坐标 [x, y]
   * @param {Array<number>} pin2 - 第二个钉子坐标 [x, y]
   * @returns {Object} 线条路径信息
   */
  calculateLinePath(pin1, pin2) {
    const [x0, y0] = pin1;
    const [x1, y1] = pin2;

    // 与原版本完全一致的距离计算
    const distance = Math.floor(Number(Math.sqrt((x1 - x0) * (x1 - x0) + (y1 - y0) * (y1 - y0))));
    const xCoords = this.linspace(x0, x1, distance);
    const yCoords = this.linspace(y0, y1, distance);

    return {
      xCoords,
      yCoords,
      distance,
      physicalLength: this.hoopDiameter / this.imageSize * distance
    };
  }

  /**
   * 预计算所有可能的线条路径
   * @param {Array<Array<number>>} pinCoords - 钉子坐标数组
   * @returns {Object} 预计算的线条缓存
   */
  precalculateLines(pinCoords) {
    const lineCache = {
      x: new Array(this.numberOfPins * this.numberOfPins),
      y: new Array(this.numberOfPins * this.numberOfPins),
      length: new Array(this.numberOfPins * this.numberOfPins).fill(0),
      weight: new Array(this.numberOfPins * this.numberOfPins).fill(1)
    };

    for (let a = 0; a < this.numberOfPins; a++) {
      for (let b = a + this.minDistance; b < this.numberOfPins; b++) {
        const linePath = this.calculateLinePath(pinCoords[a], pinCoords[b]);

        const indexAB = b * this.numberOfPins + a;
        const indexBA = a * this.numberOfPins + b;

        // 存储双向路径
        lineCache.x[indexAB] = linePath.xCoords;
        lineCache.x[indexBA] = linePath.xCoords;
        lineCache.y[indexAB] = linePath.yCoords;
        lineCache.y[indexBA] = linePath.yCoords;
        lineCache.length[indexAB] = linePath.distance;
        lineCache.length[indexBA] = linePath.distance;
      }
    }

    return lineCache;
  }

  /**
   * 计算两个钉子之间的物理距离
   * @param {Array<number>} pin1 - 第一个钉子坐标
   * @param {Array<number>} pin2 - 第二个钉子坐标
   * @returns {number} 物理距离
   */
  calculatePhysicalDistance(pin1, pin2) {
    const [x0, y0] = pin1;
    const [x1, y1] = pin2;
    const pixelDistance = Math.sqrt((x1 - x0) * (x1 - x0) + (y1 - y0) * (y1 - y0));
    return this.hoopDiameter / this.imageSize * pixelDistance;
  }

  /**
   * 检查两个钉子之间的距离是否满足最小距离要求
   * @param {number} pin1Index - 第一个钉子索引
   * @param {number} pin2Index - 第二个钉子索引
   * @returns {boolean} 是否满足最小距离要求
   */
  isValidDistance(pin1Index, pin2Index) {
    const distance = Math.abs(pin1Index - pin2Index);
    const circularDistance = Math.min(distance, this.numberOfPins - distance);
    return circularDistance >= this.minDistance;
  }

  /**
   * 获取指定钉子的有效邻居钉子列表
   * @param {number} pinIndex - 钉子索引
   * @param {Array<number>} excludePins - 要排除的钉子索引数组
   * @returns {Array<number>} 有效邻居钉子索引数组
   */
  getValidNeighbors(pinIndex, excludePins = []) {
    const neighbors = [];

    for (let offset = this.minDistance; offset < this.numberOfPins - this.minDistance; offset++) {
      const testPin = (pinIndex + offset) % this.numberOfPins;
      if (!excludePins.includes(testPin)) {
        neighbors.push(testPin);
      }
    }

    return neighbors;
  }

  /**
   * 计算圆周上两点之间的弧长
   * @param {number} pin1Index - 第一个钉子索引
   * @param {number} pin2Index - 第二个钉子索引
   * @returns {number} 弧长（弧度）
   */
  calculateArcLength(pin1Index, pin2Index) {
    const angle1 = 2 * Math.PI * pin1Index / this.numberOfPins;
    const angle2 = 2 * Math.PI * pin2Index / this.numberOfPins;
    let diff = Math.abs(angle2 - angle1);

    // 选择较短的弧长
    if (diff > Math.PI) {
      diff = 2 * Math.PI - diff;
    }

    return diff;
  }

  /**
   * 将像素坐标转换为缩放后的坐标
   * @param {Array<number>} coords - 原始坐标 [x, y]
   * @param {number} scale - 缩放因子
   * @returns {Array<number>} 缩放后的坐标
   */
  scaleCoordinates(coords, scale) {
    return [coords[0] * scale, coords[1] * scale];
  }

  /**
   * 批量缩放钉子坐标
   * @param {Array<Array<number>>} pinCoords - 钉子坐标数组
   * @param {number} scale - 缩放因子
   * @returns {Array<Array<number>>} 缩放后的钉子坐标数组
   */
  scalePinCoordinates(pinCoords, scale) {
    return pinCoords.map(coord => this.scaleCoordinates(coord, scale));
  }

  /**
   * 计算钉子的角度
   * @param {number} pinIndex - 钉子索引
   * @returns {number} 角度（弧度）
   */
  getPinAngle(pinIndex) {
    return 2 * Math.PI * pinIndex / this.numberOfPins;
  }

  /**
   * 根据角度获取最近的钉子索引
   * @param {number} angle - 角度（弧度）
   * @returns {number} 钉子索引
   */
  getClosestPinIndex(angle) {
    const normalizedAngle = ((angle % (2 * Math.PI)) + 2 * Math.PI) % (2 * Math.PI);
    return Math.round(normalizedAngle * this.numberOfPins / (2 * Math.PI)) % this.numberOfPins;
  }
}

export default GeometryCalculator;
