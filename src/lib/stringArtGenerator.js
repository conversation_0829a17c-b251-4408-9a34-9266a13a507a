/**
 * String Art Generator 主控制器
 * 协调各个模块，提供统一的API接口
 */

import ImageProcessor from './imageProcessor.js';
import GeometryCalculator from './geometryCalculator.js';
import StringArtAlgorithm from './stringArtAlgorithm.js';
import Renderer from './renderer.js';

export class StringArtGenerator {
  constructor(options = {}) {
    // 基本配置
    this.config = {
      imageSize: options.imageSize || 500,
      numberOfPins: options.numberOfPins || 288,
      maxLines: options.maxLines || 4000,
      lineWeight: options.lineWeight || 20,
      minDistance: options.minDistance || 20,
      minLoop: options.minLoop || 20,
      scale: options.scale || 2,
      hoopDiameter: options.hoopDiameter || 0.625,
      ...options
    };

    // 初始化模块
    this.imageProcessor = new ImageProcessor({
      imageSize: this.config.imageSize
    });

    this.geometryCalculator = new GeometryCalculator({
      imageSize: this.config.imageSize,
      numberOfPins: this.config.numberOfPins,
      minDistance: this.config.minDistance,
      hoopDiameter: this.config.hoopDiameter
    });

    this.algorithm = new StringArtAlgorithm({
      maxLines: this.config.maxLines,
      lineWeight: this.config.lineWeight,
      minLoop: this.config.minLoop,
      imageSize: this.config.imageSize
    });

    this.renderer = new Renderer({
      imageSize: this.config.imageSize,
      scale: this.config.scale
    });

    // 缓存数据
    this.cache = {
      pinCoords: null,
      lineCache: null,
      processedImage: null,
      lineSequence: null
    };

    // 状态
    this.state = {
      isProcessing: false,
      currentStep: 'idle',
      progress: 0
    };
  }

  /**
   * 处理图像
   * @param {HTMLImageElement|File|string} input - 图像输入
   * @param {HTMLCanvasElement} canvas - 目标画布
   * @returns {Promise<Object>} 处理结果
   */
  async processImage(input, canvas) {
    this.state.currentStep = 'processing-image';
    this.state.progress = 0;

    try {
      const result = await this.imageProcessor.processImage(input, canvas);
      this.cache.processedImage = result;
      
      this.state.progress = 1;
      return result;
    } catch (error) {
      throw new Error(`Image processing failed: ${error.message}`);
    }
  }

  /**
   * 计算钉子位置
   * @returns {Array<Array<number>>} 钉子坐标数组
   */
  calculatePins() {
    this.state.currentStep = 'calculating-pins';
    this.state.progress = 0;

    const pinCoords = this.geometryCalculator.calculatePinPositions();
    this.cache.pinCoords = pinCoords;
    
    this.state.progress = 1;
    return pinCoords;
  }

  /**
   * 预计算线条
   * @param {Function} onProgress - 进度回调
   * @returns {Promise<Object>} 线条缓存
   */
  async precalculateLines(onProgress = () => {}) {
    this.state.currentStep = 'precalculating-lines';
    this.state.progress = 0;

    if (!this.cache.pinCoords) {
      this.calculatePins();
    }

    return new Promise((resolve) => {
      const lineCache = {
        x: new Array(this.config.numberOfPins * this.config.numberOfPins),
        y: new Array(this.config.numberOfPins * this.config.numberOfPins),
        length: new Array(this.config.numberOfPins * this.config.numberOfPins).fill(0),
        weight: new Array(this.config.numberOfPins * this.config.numberOfPins).fill(1)
      };

      let completed = 0;
      const total = this.config.numberOfPins;

      const processPin = (a) => {
        if (a >= this.config.numberOfPins) {
          this.cache.lineCache = lineCache;
          this.state.progress = 1;
          resolve(lineCache);
          return;
        }

        for (let b = a + this.config.minDistance; b < this.config.numberOfPins; b++) {
          const linePath = this.geometryCalculator.calculateLinePath(
            this.cache.pinCoords[a],
            this.cache.pinCoords[b]
          );

          const indexAB = b * this.config.numberOfPins + a;
          const indexBA = a * this.config.numberOfPins + b;

          lineCache.x[indexAB] = linePath.xCoords;
          lineCache.x[indexBA] = linePath.xCoords;
          lineCache.y[indexAB] = linePath.yCoords;
          lineCache.y[indexBA] = linePath.yCoords;
          lineCache.length[indexAB] = linePath.distance;
          lineCache.length[indexBA] = linePath.distance;
        }

        completed++;
        this.state.progress = completed / total;
        onProgress({
          completed,
          total,
          progress: this.state.progress
        });

        setTimeout(() => processPin(a + 1), 0);
      };

      processPin(0);
    });
  }

  /**
   * 生成String Art
   * @param {Object} options - 生成选项
   * @returns {Promise<Object>} 生成结果
   */
  async generateStringArt(options = {}) {
    const {
      onProgress = () => {},
      onLineDrawn = () => {},
      canvas = null
    } = options;

    this.state.isProcessing = true;
    this.state.currentStep = 'generating-string-art';
    this.state.progress = 0;

    try {
      // 确保所有必要数据已准备
      if (!this.cache.processedImage) {
        throw new Error('No processed image available. Call processImage() first.');
      }
      if (!this.cache.pinCoords) {
        this.calculatePins();
      }
      if (!this.cache.lineCache) {
        await this.precalculateLines();
      }

      // 生成线条序列
      const result = await this.algorithm.generateStringArt(
        this.cache.processedImage.grayscaleData,
        this.cache.lineCache,
        this.cache.pinCoords,
        {
          onProgress: (progressData) => {
            this.state.progress = progressData.progress;
            onProgress(progressData);
          },
          onLineDrawn,
          numberOfPins: this.config.numberOfPins,
          minDistance: this.config.minDistance
        }
      );

      this.cache.lineSequence = result.lineSequence;

      // 如果提供了画布，绘制结果
      if (canvas) {
        this.renderResult(canvas, result.lineSequence);
      }

      this.state.isProcessing = false;
      this.state.currentStep = 'complete';
      this.state.progress = 1;

      return {
        ...result,
        pinCoords: this.cache.pinCoords,
        config: this.config
      };

    } catch (error) {
      this.state.isProcessing = false;
      this.state.currentStep = 'error';
      throw new Error(`String art generation failed: ${error.message}`);
    }
  }

  /**
   * 渲染结果到画布
   * @param {HTMLCanvasElement} canvas - 目标画布
   * @param {Array} lineSequence - 线条序列
   * @param {Object} options - 渲染选项
   */
  renderResult(canvas, lineSequence = null, options = {}) {
    const sequence = lineSequence || this.cache.lineSequence;
    if (!sequence || !this.cache.pinCoords) {
      throw new Error('No line sequence or pin coordinates available');
    }

    const ctx = canvas.getContext('2d');
    const renderWidth = this.config.imageSize * this.config.scale;
    const renderHeight = this.config.imageSize * this.config.scale;

    // 调整画布大小
    canvas.width = renderWidth;
    canvas.height = renderHeight;

    // 清空画布
    this.renderer.clearCanvas(ctx, renderWidth, renderHeight);

    // 绘制线条
    this.renderer.drawLineSequence(ctx, sequence, this.cache.pinCoords, {
      scale: this.config.scale,
      ...options
    });

    // 可选：绘制钉子
    if (options.showPins) {
      this.renderer.drawPins(ctx, this.cache.pinCoords, {
        scale: this.config.scale,
        showNumbers: options.showPinNumbers
      });
    }
  }

  /**
   * 创建交互式渲染器
   * @param {HTMLCanvasElement} canvas - 画布
   * @returns {Object} 交互式渲染器对象
   */
  createInteractiveRenderer(canvas) {
    if (!this.cache.lineSequence || !this.cache.pinCoords) {
      throw new Error('No line sequence available');
    }

    let currentStep = 0;
    const maxSteps = this.cache.lineSequence.length - 1;

    return {
      currentStep,
      maxSteps,
      
      nextStep: () => {
        if (currentStep < maxSteps) {
          currentStep++;
          this.renderStep(canvas, currentStep);
        }
        return currentStep;
      },
      
      previousStep: () => {
        if (currentStep > 0) {
          currentStep--;
          this.renderStep(canvas, currentStep);
        }
        return currentStep;
      },
      
      goToStep: (step) => {
        currentStep = Math.max(0, Math.min(step, maxSteps));
        this.renderStep(canvas, currentStep);
        return currentStep;
      },
      
      reset: () => {
        currentStep = 0;
        this.renderStep(canvas, currentStep);
        return currentStep;
      },
      
      complete: () => {
        currentStep = maxSteps;
        this.renderStep(canvas, currentStep);
        return currentStep;
      }
    };
  }

  /**
   * 渲染到指定步骤
   * @param {HTMLCanvasElement} canvas - 画布
   * @param {number} step - 步骤数
   */
  renderStep(canvas, step) {
    const ctx = canvas.getContext('2d');
    const renderWidth = this.config.imageSize * this.config.scale;
    const renderHeight = this.config.imageSize * this.config.scale;

    canvas.width = renderWidth;
    canvas.height = renderHeight;

    this.renderer.clearCanvas(ctx, renderWidth, renderHeight);

    if (step > 0) {
      const partialSequence = this.cache.lineSequence.slice(0, step + 1);
      this.renderer.drawLineSequence(ctx, partialSequence, this.cache.pinCoords, {
        scale: this.config.scale
      });

      // 高亮当前线条
      if (step < this.cache.lineSequence.length - 1) {
        this.renderer.highlightLine(
          ctx,
          this.cache.lineSequence[step - 1],
          this.cache.lineSequence[step],
          this.cache.pinCoords,
          { scale: this.config.scale }
        );
      }
    }
  }

  /**
   * 导出线条序列
   * @returns {string} 线条序列字符串
   */
  exportLineSequence() {
    if (!this.cache.lineSequence) {
      throw new Error('No line sequence available');
    }
    return this.cache.lineSequence.join(',');
  }

  /**
   * 导入线条序列
   * @param {string} sequenceString - 线条序列字符串
   */
  importLineSequence(sequenceString) {
    try {
      const sequence = sequenceString.split(',').map(num => parseInt(num.trim()));
      
      // 验证序列
      const validation = this.algorithm.validateLineSequence(
        sequence,
        this.config.numberOfPins,
        this.config.minDistance
      );
      
      if (!validation.isValid) {
        throw new Error(`Invalid line sequence: ${validation.errors.join(', ')}`);
      }
      
      this.cache.lineSequence = sequence;
      return validation;
    } catch (error) {
      throw new Error(`Failed to import line sequence: ${error.message}`);
    }
  }

  /**
   * 获取当前状态
   * @returns {Object} 当前状态
   */
  getState() {
    return {
      ...this.state,
      hasProcessedImage: !!this.cache.processedImage,
      hasPinCoords: !!this.cache.pinCoords,
      hasLineCache: !!this.cache.lineCache,
      hasLineSequence: !!this.cache.lineSequence
    };
  }

  /**
   * 重置生成器
   */
  reset() {
    this.cache = {
      pinCoords: null,
      lineCache: null,
      processedImage: null,
      lineSequence: null
    };
    
    this.state = {
      isProcessing: false,
      currentStep: 'idle',
      progress: 0
    };
  }
}

export default StringArtGenerator;
