import enLocales from '../locales/en.json';
import cnLocales from '../locales/cn.json';
import hkLocales from '../locales/hk.json';
import jaLocales from '../locales/ja.json';
import koLocales from '../locales/ko.json';
import esLocales from '../locales/es.json';
import ptLocales from '../locales/pt.json';
import itLocales from '../locales/it.json';
import frLocales from '../locales/fr.json';
import deLocales from '../locales/de.json';
import trLocales from '../locales/tr.json';

export const locales = {
  "en": {
    name: "English",
    locales: enLocales
  },
  "fr": {
    name: "Français",
    locales: frLocales
  },
  "de": {
    name: "Deutsch",
    locales: deLocales
  },
  "tr": {
    name: "<PERSON>ürkçe",
    locales: trLocales
  },
  "es": {
    name: "Español",
    locales: esLocales
  },
  "ja": {
    name: "日本語",
    locales: jaLocales
  },
  "ko": {
    name: "한국어",
    locales: koLocales
  },
  "pt": {
    name: "Português",
    locales: ptLocales
  },
  "it": {
    name: "Italiano",
    locales: itLocales
  },
  "zh-CN": {
    name: "简体中文",
    locales: cnLocales
  },
  "zh-HK": {
    name: "繁體中文",
    locales: hkLocales
  },
};

export function getLocale(locale) {
  return locales[locale] || locales.en;
}

export function getTranslation(locale, key, ...params) {
  const localeData = getLocale(locale);

  // 直接访问键，不再使用 split
  let result = localeData.locales[key];

  if (result === undefined) {
    console.log(`Translation not found for key: ${key} in locale: ${locale}`);
    return key;
  }

  // 如果有参数，进行字符串替换
  if (params.length > 0) {
    // 支持 %d, %s, %f 等占位符
    let paramIndex = 0;
    result = result.replace(/%[dsf]/g, () => {
      if (paramIndex < params.length) {
        return params[paramIndex++];
      }
      return '';
    });
  }

  return result;
}

