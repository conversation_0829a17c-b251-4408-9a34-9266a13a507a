/**
 * 渲染模块
 * 负责Canvas绘制和可视化功能
 */

export class Renderer {
  constructor(options = {}) {
    this.imageSize = options.imageSize || 500;
    this.scale = options.scale || 2;
    this.lineWidth = options.lineWidth || 0.3;
    this.pinRadius = options.pinRadius || 2;
    this.backgroundColor = options.backgroundColor || '#ffffff';
    this.lineColor = options.lineColor || '#000000';
    this.pinColor = options.pinColor || '#ff0000';
    this.highlightColor = options.highlightColor || '#ff0000';
  }

  /**
   * 创建并配置画布
   * @param {number} width - 画布宽度
   * @param {number} height - 画布高度
   * @returns {Object} 包含画布和上下文的对象
   */
  createCanvas(width, height) {
    const canvas = document.createElement('canvas');
    canvas.width = width;
    canvas.height = height;
    const ctx = canvas.getContext('2d');
    
    // 设置默认样式
    ctx.fillStyle = this.backgroundColor;
    ctx.fillRect(0, 0, width, height);
    ctx.strokeStyle = this.lineColor;
    ctx.lineWidth = this.lineWidth;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';
    
    return { canvas, ctx };
  }

  /**
   * 清空画布
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} width - 画布宽度
   * @param {number} height - 画布高度
   */
  clearCanvas(ctx, width, height) {
    ctx.fillStyle = this.backgroundColor;
    ctx.fillRect(0, 0, width, height);
  }

  /**
   * 绘制钉子
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {Array} pinCoords - 钉子坐标数组
   * @param {Object} options - 绘制选项
   */
  drawPins(ctx, pinCoords, options = {}) {
    const {
      scale = this.scale,
      radius = this.pinRadius,
      color = this.pinColor,
      showNumbers = false,
      fontSize = 10
    } = options;

    ctx.save();
    ctx.fillStyle = color;
    ctx.font = `${fontSize}px Arial`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';

    pinCoords.forEach((coord, index) => {
      const [x, y] = coord;
      const scaledX = x * scale;
      const scaledY = y * scale;

      // 绘制钉子
      ctx.beginPath();
      ctx.arc(scaledX, scaledY, radius, 0, 2 * Math.PI);
      ctx.fill();

      // 绘制编号（可选）
      if (showNumbers) {
        ctx.fillStyle = '#ffffff';
        ctx.fillText(index.toString(), scaledX, scaledY);
        ctx.fillStyle = color;
      }
    });

    ctx.restore();
  }

  /**
   * 绘制单条线
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {Array} fromCoord - 起始坐标 [x, y]
   * @param {Array} toCoord - 结束坐标 [x, y]
   * @param {Object} options - 绘制选项
   */
  drawLine(ctx, fromCoord, toCoord, options = {}) {
    const {
      scale = this.scale,
      color = this.lineColor,
      width = this.lineWidth,
      alpha = 1
    } = options;

    ctx.save();
    ctx.strokeStyle = color;
    ctx.lineWidth = width;
    ctx.globalAlpha = alpha;

    ctx.beginPath();
    ctx.moveTo(fromCoord[0] * scale, fromCoord[1] * scale);
    ctx.lineTo(toCoord[0] * scale, toCoord[1] * scale);
    ctx.stroke();

    ctx.restore();
  }

  /**
   * 绘制线条序列
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {Array} lineSequence - 线条序列
   * @param {Array} pinCoords - 钉子坐标数组
   * @param {Object} options - 绘制选项
   */
  drawLineSequence(ctx, lineSequence, pinCoords, options = {}) {
    const {
      scale = this.scale,
      color = this.lineColor,
      width = this.lineWidth,
      alpha = 1,
      maxLines = lineSequence.length - 1,
      fadeEffect = false
    } = options;

    ctx.save();
    ctx.strokeStyle = color;
    ctx.lineWidth = width;

    for (let i = 1; i < Math.min(lineSequence.length, maxLines + 1); i++) {
      const fromPin = lineSequence[i - 1];
      const toPin = lineSequence[i];
      
      if (fromPin >= 0 && fromPin < pinCoords.length && 
          toPin >= 0 && toPin < pinCoords.length) {
        
        // 计算透明度（渐变效果）
        let lineAlpha = alpha;
        if (fadeEffect) {
          lineAlpha = alpha * (i / lineSequence.length);
        }
        
        ctx.globalAlpha = lineAlpha;
        
        ctx.beginPath();
        ctx.moveTo(pinCoords[fromPin][0] * scale, pinCoords[fromPin][1] * scale);
        ctx.lineTo(pinCoords[toPin][0] * scale, pinCoords[toPin][1] * scale);
        ctx.stroke();
      }
    }

    ctx.restore();
  }

  /**
   * 高亮显示特定线条
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} fromPin - 起始钉子索引
   * @param {number} toPin - 结束钉子索引
   * @param {Array} pinCoords - 钉子坐标数组
   * @param {Object} options - 绘制选项
   */
  highlightLine(ctx, fromPin, toPin, pinCoords, options = {}) {
    const {
      scale = this.scale,
      color = this.highlightColor,
      width = this.lineWidth * 2
    } = options;

    if (fromPin >= 0 && fromPin < pinCoords.length && 
        toPin >= 0 && toPin < pinCoords.length) {
      
      this.drawLine(ctx, pinCoords[fromPin], pinCoords[toPin], {
        scale,
        color,
        width
      });
    }
  }

  /**
   * 绘制进度指示器
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} progress - 进度 (0-1)
   * @param {Object} options - 绘制选项
   */
  drawProgress(ctx, progress, options = {}) {
    const {
      x = 10,
      y = 10,
      width = 200,
      height = 20,
      backgroundColor = '#e0e0e0',
      progressColor = '#4caf50',
      textColor = '#000000',
      showText = true
    } = options;

    ctx.save();

    // 绘制背景
    ctx.fillStyle = backgroundColor;
    ctx.fillRect(x, y, width, height);

    // 绘制进度
    ctx.fillStyle = progressColor;
    ctx.fillRect(x, y, width * progress, height);

    // 绘制边框
    ctx.strokeStyle = '#cccccc';
    ctx.lineWidth = 1;
    ctx.strokeRect(x, y, width, height);

    // 绘制文本
    if (showText) {
      ctx.fillStyle = textColor;
      ctx.font = '12px Arial';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(
        `${Math.round(progress * 100)}%`,
        x + width / 2,
        y + height / 2
      );
    }

    ctx.restore();
  }

  /**
   * 绘制信息面板
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {Object} info - 信息对象
   * @param {Object} options - 绘制选项
   */
  drawInfoPanel(ctx, info, options = {}) {
    const {
      x = 10,
      y = 50,
      backgroundColor = 'rgba(255, 255, 255, 0.9)',
      textColor = '#000000',
      fontSize = 12,
      padding = 10
    } = options;

    const lines = [];
    if (info.currentLine !== undefined) lines.push(`Line: ${info.currentLine}`);
    if (info.totalLines !== undefined) lines.push(`Total: ${info.totalLines}`);
    if (info.currentPin !== undefined) lines.push(`Pin: ${info.currentPin}`);
    if (info.threadLength !== undefined) lines.push(`Length: ${info.threadLength.toFixed(2)}`);

    if (lines.length === 0) return;

    ctx.save();

    // 计算面板尺寸
    ctx.font = `${fontSize}px Arial`;
    const maxWidth = Math.max(...lines.map(line => ctx.measureText(line).width));
    const panelWidth = maxWidth + padding * 2;
    const panelHeight = lines.length * (fontSize + 4) + padding * 2;

    // 绘制背景
    ctx.fillStyle = backgroundColor;
    ctx.fillRect(x, y, panelWidth, panelHeight);

    // 绘制边框
    ctx.strokeStyle = '#cccccc';
    ctx.lineWidth = 1;
    ctx.strokeRect(x, y, panelWidth, panelHeight);

    // 绘制文本
    ctx.fillStyle = textColor;
    ctx.textAlign = 'left';
    ctx.textBaseline = 'top';

    lines.forEach((line, index) => {
      ctx.fillText(
        line,
        x + padding,
        y + padding + index * (fontSize + 4)
      );
    });

    ctx.restore();
  }

  /**
   * 导出画布为图像
   * @param {HTMLCanvasElement} canvas - 画布元素
   * @param {string} format - 图像格式 ('png', 'jpeg', 'webp')
   * @param {number} quality - 图像质量 (0-1)
   * @returns {string} 图像数据URL
   */
  exportImage(canvas, format = 'png', quality = 0.9) {
    return canvas.toDataURL(`image/${format}`, quality);
  }

  /**
   * 下载画布为图像文件
   * @param {HTMLCanvasElement} canvas - 画布元素
   * @param {string} filename - 文件名
   * @param {string} format - 图像格式
   * @param {number} quality - 图像质量
   */
  downloadImage(canvas, filename = 'string-art', format = 'png', quality = 0.9) {
    const dataURL = this.exportImage(canvas, format, quality);
    const link = document.createElement('a');
    link.download = `${filename}.${format}`;
    link.href = dataURL;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  /**
   * 调整画布大小
   * @param {HTMLCanvasElement} canvas - 画布元素
   * @param {number} width - 新宽度
   * @param {number} height - 新高度
   * @param {boolean} preserveContent - 是否保留内容
   */
  resizeCanvas(canvas, width, height, preserveContent = false) {
    if (preserveContent) {
      const imageData = canvas.getContext('2d').getImageData(0, 0, canvas.width, canvas.height);
      canvas.width = width;
      canvas.height = height;
      canvas.getContext('2d').putImageData(imageData, 0, 0);
    } else {
      canvas.width = width;
      canvas.height = height;
    }
  }
}

export default Renderer;
