import { generateAlternates } from '@/lib/hreflang';
import { getTranslation } from '@/lib/i18n';
import { ToastProvider } from '@heroui/react';
import Hero from '@/app/components/ui/Hero';
import WhatIsMaker from '@/app/components/ui/WhatIsMaker';
import HowToUseMaker from '@/app/components/ui/HowToUseMaker';
import MakerFAQ from '@/app/components/ui/MakerFAQ';

export async function generateMetadata({ params }) {
  const title = 'Create with String Art Maker';
  const description = 'Transform your images into stunning string art with our string art maker free online. Upload, customize with 288 pins and 4000 lines, and download your pattern now!';
  const currentLocale = params.locale || 'en';

  return {
    title: title,
    description: description,
    alternates: generateAlternates('/string-art-maker', currentLocale),
  }
}

export default function CurrentPage({ params: { locale } }) {
  const t = function (key) {
    return getTranslation(locale, key);
  }

  return (
    <>
      <ToastProvider placement="top-center" toastOffset={230} />
      <div>
        <div id="hero" className="section bg-gradient-to-b from-blue-50 dark:from-black to-white dark:to-black">
          <Hero locale={locale} title={t('String Art Maker')} subtitle={t('Craft Stunning String Art Patterns Online for Free')} showPH={false} />
        </div>
        <div id="what-is" className="page-container section bg-gray-100 dark:bg-gray-800 rounded mt-20">
          <WhatIsMaker locale={locale}></WhatIsMaker>
        </div>
        <div id="how-to-use" className="page-container section bg-gray-100 dark:bg-gray-800 rounded mt-20">
          <HowToUseMaker locale={locale}></HowToUseMaker>
        </div>
        <div id="faq" className="page-container section">
          <MakerFAQ locale={locale} />
        </div>
      </div>
    </>
  );
}
