import { generateAlternates } from '@/lib/hreflang';
import Gallery from '@/app/components/ui/Gallery';
import { getTranslation } from '@/lib/i18n';

export async function generateMetadata({ params }) {
  const title = 'Explore Stunning String Art Patterns';
  const description = 'Explore our String Art Gallery to see stunning patterns created with our free online string art generator. Hover to view original images and get inspired!';
  const currentLocale = params.locale || 'en';

  return {
    title: title,
    description: description,
    alternates: generateAlternates('/gallery', currentLocale),
  }
}

export default function CurrentPage({ params: { locale } }) {
  const t = function (key) {
    return getTranslation(locale, key);
  }

  return (
    <div className='section px-10 md:px-20 mt-10 text-center'>
      <h1 className="text-5xl font-bold text-primary mb-2">{t("Discover Beautiful String Art Creations")}</h1>
      <p className='text-3xl text-subtext mb-8'>
        {t("Showcasing Artistry with Our Free String Art Generator")}
      </p>
      <Gallery locale={locale}></Gallery>
    </div>
  );
}
