import "../globals.css";

import GoogleAnalytics from '../components/google/GoogleAnalytics';
import GoogleAdsense from '../components/google/GoogleAdsense';
import UmamiAnalytics from '../components/common/UmamiAnalytics';

import { Providers } from "../providers";

import MyNavbar from '../components/ui/MyNavbar';
import MyFooter from '../components/ui/MyFooter';
import { generateAlternates } from "@/lib/hreflang";


export async function generateMetadata({ params }) {
  const title = 'String Art Generator - Free Online Pattern Creator';
  const description = 'Create stunning string art patterns with our free online String Art Generator. Upload your image, customize pins and lines, and download your design now!';
  const image = `${process.env.NEXT_PUBLIC_SITE_URL}/images/og.png`;
  const currentLocale = params.locale || 'en';

  return {
    title: {
      default: title,
      template: '%s | String Art Generator'
    },
    description: description,
    authors: [{ name: 'String Art Generator' }],
    robots: 'index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1',
    alternates: generateAlternates('', currentLocale),
    openGraph: {
      title: title,
      description: description,
      type: 'website',
      url: `${process.env.NEXT_PUBLIC_SITE_URL}`,
      siteName: 'String Art Generator',
      images: [{
        url: image
      }]
    },
    twitter: {
      card: 'summary_large_image',
      site: '@String Art Generator',
      title: title,
      description: description,
      images: [image]
    },
    icons: {
      icon: [
        { url: '/favicon.ico' },
        { url: '/images/logo.png', sizes: '32x32', type: 'image/png' },
        { url: '/images/logo.png', sizes: '16x16', type: 'image/png' }
      ],
      apple: [
        { url: '/images/logo.png', sizes: '180x180' }
      ]
    }
  }
}

export default function RootLayout({ children, params }) {
  const locale = params?.locale || 'en';

  return (
    <html lang={locale} suppressHydrationWarning>
      <head>
        <GoogleAdsense />
        <GoogleAnalytics />
        <UmamiAnalytics />
      </head>
      <body className="bg-background text-foreground">
        <Providers>
          <MyNavbar locale={locale} />
          {children}
          <MyFooter locale={locale} />
        </Providers>
      </body>
    </html>
  );
}
