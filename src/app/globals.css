@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: Arial, Helvetica, sans-serif;
}

.page-container {
  @apply max-w-[1200px] mx-auto px-6;
}

.section {
  @apply py-4;
}

.article-content img,
.article-content video {
  width: 100%;
  height: auto;
  max-width: 100%;
  display: block;
  margin: 0 auto;
}

.article-content pre {
  white-space: pre-wrap;
  word-break: break-all;
  font-family: inherit;
  margin-top: 10px;
  margin-bottom: 10px;
}

.tweet-card pre {
  word-break: break-all;
}

/* Blog content styles */
.blog-content {
  @apply text-foreground;
}

.blog-content h1 {
  @apply text-3xl font-bold mb-6 mt-8 text-foreground;
}

.blog-content h2 {
  @apply text-2xl font-semibold mb-4 mt-6 text-foreground;
}

.blog-content h3 {
  @apply text-xl font-medium mb-3 mt-5 text-foreground;
}

.blog-content h4 {
  @apply text-lg font-medium mb-2 mt-4 text-foreground;
}

.blog-content p {
  @apply mb-4 text-foreground leading-relaxed;
}

.blog-content ul,
.blog-content ol {
  @apply mb-4 ml-6;
}

.blog-content li {
  @apply mb-2 text-foreground;
}

.blog-content ul li {
  @apply list-disc;
}

.blog-content ol li {
  @apply list-decimal;
}

.blog-content blockquote {
  @apply border-l-4 border-primary pl-4 italic mb-4 text-gray-600;
}

.blog-content code {
  @apply bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded text-sm font-mono;
}

.blog-content pre {
  @apply bg-gray-100 dark:bg-gray-800 p-4 rounded mb-4 overflow-x-auto;
}

.blog-content pre code {
  @apply bg-transparent p-0;
}

.blog-content a {
  @apply text-primary hover:underline;
}

.blog-content img {
  @apply w-full h-auto max-w-full block mx-auto my-6 rounded;
}

.blog-content table {
  @apply w-full border-collapse mb-4;
}

.blog-content th,
.blog-content td {
  @apply border border-gray-300 dark:border-gray-600 px-4 py-2 text-left;
}

.blog-content th {
  @apply bg-gray-100 dark:bg-gray-800 font-semibold;
}