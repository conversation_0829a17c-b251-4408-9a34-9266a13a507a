'use client';
import { Accordion, AccordionItem } from "@heroui/react";
import { getTranslation } from '@/lib/i18n';

export default function MakerFAQ({ locale = 'en' }) {
  const t = function (key) {
    return getTranslation(locale, key);
  }

  const faq = [
    {
      label: "What is a string art maker?",
      question: t("What is a string art maker?"),
      answer: t("Our string art maker is an online tool within String Art Generator that converts images into string art patterns using nail and thread connections."),
    },
    {
      label: "Is String Art Maker free?",
      question: t("Is String Art Maker free?"),
      answer: t("Yes, our string art maker free offers all features at no cost, with no subscriptions or hidden fees."),
    },
    {
      label: "What image formats does String Art Maker support?",
      question: t("What image formats does String Art Maker support?"),
      answer: t("You can upload JPEG, PNG, or WEBP images to our string art maker online for conversion."),
    },
    {
      label: "Do I need design skills to use String Art Maker?",
      question: t("Do I need design skills to use String Art Maker?"),
      answer: t("No, our string art maker is user-friendly, requiring no technical expertise to create stunning patterns."),
    },
    {
      label: "What are the default settings for String Art Maker?",
      question: t("What are the default settings for String Art Maker?"),
      answer: t("The default settings are 288 pins, 4000 lines, and a line weight of 20, adjustable for custom designs."),
    },
    {
      label: "How long does it take to generate a pattern?",
      question: t("How long does it take to generate a pattern?"),
      answer: t("Our string art maker free online typically generates patterns in seconds to a minute, depending on image complexity."),
    },
    {
      label: "Can I use the patterns for physical crafting?",
      question: t("Can I use the patterns for physical crafting?"),
      answer: t("Yes, download the TXT sequence to guide your nail and thread placement for physical string art."),
    },
    {
      label: "What output formats are available?",
      question: t("What output formats are available?"),
      answer: t("Our string art maker provides TXT files for sequences and SVG files for printing or CNC use."),
    },
  ]

  return (
    <>
      <h2 className="text-2xl font-bold px-2 py-4">{t('Frequently Asked Questions about String Art Generator')}</h2>
      < Accordion
        selectionMode="multiple"
        className="border-foreground/10 border-[1px] rounded-2xl px-6"
      >
        {
          faq.map((item, index) => (
            <AccordionItem key={index} aria-label={item.label} title={item.question} HeadingComponent="h3">
              {item.answer}
            </AccordionItem>
          ))
        }
      </Accordion>
    </>
  )
}
