"use client";
import { getTranslation } from '@/lib/i18n';
import { useState } from 'react';

export default function Gallery({ locale = 'en' }) {
  const [hoveredImage, setHoveredImage] = useState(null);

  const t = function (key) {
    return getTranslation(locale, key);
  }

  const images = [
    {
      title: 'luffy',
      url: '/gallery/luffy.jpg',
      arturl: '/gallery/luffy-Art.png'
    },
    {
      title: 'zoro',
      url: '/gallery/zoro.jpeg',
      arturl: '/gallery/zoro-Art.png'
    },
    {
      title: 'Sanji',
      url: '/gallery/Sanji.jpeg',
      arturl: '/gallery/Sanji-Art.png'
    },
    {
      title: 'Brook',
      url: '/gallery/Brook.jpg',
      arturl: '/gallery/Brook-Art.png'
    },
    {
      title: 'Frank<PERSON>',
      url: '/gallery/Franky.jpg',
      arturl: '/gallery/Franky-Art.png'
    },
    {
      title: '<PERSON><PERSON>',
      url: '/gallery/Jinbe.png',
      arturl: '/gallery/Jinbe-Art.png'
    },
    {
      title: 'Nami',
      url: '/gallery/Nami.webp',
      arturl: '/gallery/Nami-Art.png'
    },
    {
      title: 'Robin',
      url: '/gallery/Robin.webp',
      arturl: '/gallery/Robin-Art.png'
    },
    {
      title: 'Usopp',
      url: '/gallery/Usopp.webp',
      arturl: '/gallery/Usopp-Art.png'
    },
  ]

  return (
    <div className="md:p-10">
      <div className="flex flex-wrap gap-6 justify-center">
        {images.map((image, index) => (
          <div
            key={index}
            className="flex flex-col items-center relative"
            onMouseEnter={() => setHoveredImage(index)}
            onMouseLeave={() => setHoveredImage(null)}
          >
            {hoveredImage === index ? (
              <img
                src={image.url}
                alt={image.title}
                className="w-[280px] h-[280px] object-cover rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300 p-4"
              />
            ) : (
              <img
                src={image.arturl}
                alt={image.title}
                className="w-[280px] h-[280px] object-cover rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300 p-4"
              />
            )}
          </div>
        ))}
      </div>
    </div>
  )
}
