import { getTranslation } from '@/lib/i18n';

export default function WhatIs({ locale = 'en' }) {
  const t = function (key) {
    return getTranslation(locale, key);
  }

  return (
    <>
      <h2 className="text-2xl font-bold px-2 py-4">{t('What is String Art Maker?')}</h2>
      <div className="px-2">
        <p className='mb-4'>
          {t("String Art Maker is a powerful feature of String Art Generator, designed to turn your images into intricate string art patterns effortlessly. Our string art maker online allows anyone—DIY enthusiasts, artists, or educators—to create beautiful designs using a simple, free tool. By uploading a photo and adjusting settings like 288 pins, 4000 lines, and a line weight of 20, you can generate professional-grade patterns for crafting or CNC production. Unlike other tools, our string art maker free requires no subscriptions, making creativity accessible to all.")}
        </p>
        <p className='mb-4'>
          {t("With our free string art maker online, you can explore the art of string design, where threads are stretched between nails to form captivating patterns. Whether you're creating personalized gifts or teaching geometric concepts, String Art Maker simplifies the process with an intuitive interface and high-quality outputs, including TXT sequences and SVG files. Discover the joy of string art with String Art Generator today!")}
        </p>
      </div>
    </>
  )
}
