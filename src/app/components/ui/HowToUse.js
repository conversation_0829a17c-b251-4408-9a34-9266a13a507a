import { getTranslation } from '@/lib/i18n';

export default function HowToUse({ locale = 'en' }) {
  const t = function (key) {
    return getTranslation(locale, key);
  }

  return (
    <>
      <h2 className="text-2xl font-bold px-2 py-4">{t('How to Use String Art Generator')}</h2>
      <div className="px-2">
        <p className='mb-4'>
          {t('Follow these steps to create your own string art patterns with our string art generator free online:')}
        </p>
        <ul className='list-decimal list-inside pl-2'>
          <li className='mb-2'><strong>{t("Upload Your Image")}: </strong>{t("Click or drag a JPEG, PNG, or WEBP image into the upload area. High-contrast images like portraits or logos work best.")}</li>
          <li className='mb-2'><strong>{t("Preview Your Image")}: </strong>{t("View your image on the canvas to confirm it's suitable for string art conversion.")}</li>
          <li className='mb-2'><strong>{t("Set Parameters")}: </strong>{t("Adjust the number of pins (default 288), lines (default 4000), and line weight (default 20) using the input fields for optimal results.")}</li>
          <li className='mb-2'><strong>{t("Generate Pattern")}: </strong>{t("Click the 'Convert to String Art' button to start the process. Watch the progress bar as our string art generator creates your design.")}</li>
          <li className='mb-2'><strong>{t("Review Results")}: </strong>{t("See the generated string art on the output canvas, along with the nail connection sequence.")}</li>
          <li className='mb-2'><strong>{t("Download Files")}: </strong>{t("Save your pattern as a TXT file for manual crafting or an SVG file for printing or CNC machines.")}</li>
          <li><strong>{t("Craft Your Art")}: </strong>{t("Use the downloaded sequence to guide your physical string art project on a wooden board with nails and thread.")}</li>
        </ul>
      </div>
    </>
  )
}
