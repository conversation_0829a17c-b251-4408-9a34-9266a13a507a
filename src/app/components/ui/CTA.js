import { getTranslation } from '@/lib/i18n';
import { <PERSON><PERSON>, <PERSON> } from '@heroui/react';

export default function CTA({ locale = 'en' }) {
  const t = function (key) {
    return getTranslation(locale, key);
  }

  return (
    <div className='section text-center bg-gradient-to-b from-blue-50 dark:from-black to-white dark:to-black my-20'>
      <h2 className="text-2xl font-bold px-2 py-4">{t('Try String Art Generator Now!')}</h2>
      <p>{t("Create stunning string art patterns with our free online String Art Generator. Upload your image, customize pins and lines, and download your design now!")}</p>
      <Link href="/">
        <Button color='primary' className='mt-10'>{t('Try String Art Generator Now!')}</Button>
      </Link>
    </div>
  )
}
