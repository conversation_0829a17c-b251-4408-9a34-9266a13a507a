import { getTranslation } from '@/lib/i18n';
import { Card, CardHeader, CardBody } from '@heroui/react';

export default function KeyFeatures({ locale = 'en' }) {
  const t = function (key) {
    return getTranslation(locale, key);
  }

  const keyFeatures = [
    {
      title: t("Free Online Access"),
      description: t("Create stunning string art patterns anytime with our string art generator free. No subscriptions or fees—just upload your image and start designing instantly."),
    },
    {
      title: t("Customizable Pin and Line Settings"),
      description: t("Fine-tune your string art with 288 pins, 4000 lines, and a line weight of 20 as default settings. Adjust these parameters to craft intricate, personalized patterns."),
    },
    {
      title: t("Image Upload and Preview"),
      description: t("Upload JPEG, PNG, or WEBP images to our string art generator online. Preview your image in real-time on a canvas to ensure it's ready for conversion."),
    },
    {
      title: t("High-Quality Output Formats"),
      description: t("Download your pattern as a TXT file for nail sequences or an SVG file for printing or CNC use. Our free string art generator online delivers professional results."),
    },
    {
      title: t("Real-Time Progress Tracking"),
      description: t("Monitor the creation process with live updates. Our string art generator shows generation progress, so you know when your pattern is complete."),
    },
    {
      title: t("User-Friendly Interface"),
      description: t("Designed for all skill levels, String Art Generator offers a clean, intuitive interface, making string art creation effortless and enjoyable."),
    },
  ]

  return (
    <>
      <h2 className="text-2xl font-bold px-2 py-4">{t('Key Features of Our String Art Converter')}</h2>
      <div className="flex flex-wrap gap-8 justify-between md:justify-start" >
        {
          keyFeatures.map((feature) => (
            <Card
              shadow="none"
              disableRipple
              className="select-none box-border border-foreground/10 border-[1px] min-w-[160px]  max-w-full md:max-w-[30%]  p-2 flex-shrink-0 hover:shadow-2xl hover:scale-105 transition-all duration-300 ease-in-out"
              radius="lg"
              key={feature.title}
            >
              <CardHeader className="justify-between gap-5">
                <h3 className="text-lg font-bold px-2">{feature.title}</h3>
              </CardHeader>
              <CardBody>
                <p>{feature.description}</p>
              </CardBody>
            </Card>
          ))
        }
      </div>
    </>
  )
}
