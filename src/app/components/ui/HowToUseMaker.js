import { getTranslation } from '@/lib/i18n';

export default function HowToUseMaker({ locale = 'en' }) {
  const t = function (key) {
    return getTranslation(locale, key);
  }

  return (
    <>
      <h2 className="text-2xl font-bold px-2 py-4">{t('How to Use String Art Maker?')}</h2>
      <div className="px-2">
        <p className='mb-4'>
          {t('Creating string art with our string art maker is easy and fun. Follow these steps to get started:')}
        </p>
        <ul className='list-decimal list-inside pl-2'>
          <li className='mb-2'><strong>{t("Upload Your Image")}: </strong>{t("Visit our string art maker online and upload a JPEG, PNG, or WEBP image. High-contrast images like portraits or logos work best.")}</li>
          <li className='mb-2'><strong>{t("Preview Your Image")}: </strong>{t("View your image on the canvas to ensure it's suitable for conversion.")}</li>
          <li className='mb-2'><strong>{t("Set Parameters")}: </strong>{t("Adjust the number of pins (default 288), lines (default 4000), and line weight (default 20) for a tailored pattern.")}</li>
          <li className='mb-2'><strong>{t("Generate Pattern")}: </strong>{t("Click 'Convert to String Art' to create your design with our string art maker free. Watch the progress in real-time.")}</li>
          <li className='mb-2'><strong>{t("Review and Download")}: </strong>{t("See the generated pattern on the canvas and download it as a TXT file for crafting or an SVG file for printing or CNC use.")}</li>
          <li><strong>{t("Craft Your Art")}: </strong>{t("Use the TXT sequence to guide your physical string art project on a board with nails and thread.")}</li>
        </ul>
      </div>
    </>
  )
}
