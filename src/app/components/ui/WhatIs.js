import { getTranslation } from '@/lib/i18n';

export default function WhatIs({ locale = 'en' }) {
  const t = function (key) {
    return getTranslation(locale, key);
  }

  return (
    <>
      <h2 className="text-2xl font-bold px-2 py-4">{t('What is String Art Generator?')}</h2>
      <div className="px-2">
        <p className='mb-4'>
          {t("String Art Generator is a powerful, free online tool that transforms your images into captivating string art patterns. With our string art generator, you can upload a photo, customize settings like 288 pins, 4000 lines, and a line weight of 20, and generate downloadable patterns for crafting. Perfect for DIY enthusiasts, artists, and educators, our string art generator online simplifies the creation process with an intuitive interface and advanced algorithms. Whether you're crafting unique home decor or exploring geometric art, String Art Generator makes it accessible and fun.")}
        </p>
        <p className='mb-4'>
          {t("Our string art generator free online offers high-quality outputs at no cost, including TXT sequences for manual crafting and SVG files for printing or CNC production. Join a global community of creators who use String Art Generator to bring their artistic visions to life, no coding skills required.")}
        </p>
      </div>
    </>
  )
}
