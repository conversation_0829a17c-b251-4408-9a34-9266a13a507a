// MyNavbar.js (服务端组件)
import {
  Navbar,
  Navbar<PERSON><PERSON>,
  Navbar<PERSON>ontent,
  NavbarItem,
  Link,
} from "@heroui/react";
import { getTranslation } from "@/lib/i18n";
import LanguageSwitcher from "./LanguageSwitcher"; // 新的客户端组件
import MobileMenu from "./MobileMenu";
import { ThemeSwitcher } from "./ThemeSwitcher";
import { getLinkHref } from "@/lib/hreflang";

export default function MyNavbar({ locale = 'en' }) {
  const t = function (key) {
    return getTranslation(locale, key);
  }

  const getLink = (anchor) => {
    if (locale == 'en') {
      return `${process.env.NEXT_PUBLIC_SITE_URL}/#${anchor}`;
    }
    return `${process.env.NEXT_PUBLIC_SITE_URL}/${locale}#${anchor}`;
  }

  return (
    <Navbar classNames={{
      wrapper: "page-container"
    }}>
      <NavbarBrand>
        <Link href={getLinkHref(locale, "")} className="text-foreground">
          <img src="/images/logo.png" alt="stringartgenerator" width={32} height={32} />
          <p className="font-bold text-inherit mx-3 text-2xl">
            {t('String Art Generator')}
          </p>
        </Link>
      </NavbarBrand>
      <NavbarContent className="hidden md:flex gap-6" justify="center">
        <NavbarItem>
          <Link color="foreground" href={getLinkHref(locale, 'gallery')}>
            {t('Gallery')}
          </Link>
        </NavbarItem>
        <NavbarItem>
          <Link color="foreground" href={getLink('key-features')}>
            {t('Key Features')}
          </Link>
        </NavbarItem>
        <NavbarItem>
          <Link color="foreground" href={getLink('how-to-use')}>
            {t('How To Use')}
          </Link>
        </NavbarItem>
        <NavbarItem>
          <Link color="foreground" href={getLink("use-case")}>
            {t('Feedback')}
          </Link>
        </NavbarItem>
        <NavbarItem>
          <Link color="foreground" href={getLink("faq")}>
            {t('FAQ')}
          </Link>
        </NavbarItem>
        <NavbarItem>
          <Link color="foreground" href={getLinkHref(locale, "blog")}>
            {t('Blog')}
          </Link>
        </NavbarItem>
      </NavbarContent>
      <NavbarContent justify="end">
        <NavbarItem className="hidden md:flex gap-4">
          <LanguageSwitcher locale={locale} />
          <ThemeSwitcher />
        </NavbarItem>
      </NavbarContent>
      <MobileMenu locale={locale} />
    </Navbar >
  );
}
